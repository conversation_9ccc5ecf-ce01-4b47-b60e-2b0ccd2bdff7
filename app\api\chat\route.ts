// app/api/chat/route.ts
import { NextRequest } from 'next/server';
import { google } from '@ai-sdk/google';
import { openai } from '@ai-sdk/openai';
import { generateText, tool } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';
import { query } from '@/lib/db';
import { JSDOM } from 'jsdom';
import { addReminderWithRetry } from '@/lib/reminder-utils';
import {
    AddReminderInput,
    GetRemindersInput,
    UpdateReminderInput,
    DeleteReminderInput,
    type ReminderResult,
    type GetR<PERSON>indersResult,
    type UpdateReminderResult,
    type DeleteR<PERSON>inderR<PERSON>ult
} from '@/types/tools';
import axios from 'axios';

// Configure route options
export const maxDuration = 60;
export const dynamic = 'force-dynamic';

// Maximum retries for AI generation
const MAX_RETRIES = 3;
// Delay between retries (in milliseconds)
const RETRY_DELAY = 1000;

let redis: any = null;

interface AIError extends Error {
    code?: string;
    status?: number;
    retryable?: boolean;
}
// Utility function to determine if an error is retryable
function isRetryableError(error: any): boolean {
    if (!error) return false;

    // Common retryable error codes
    const retryableCodes = [
        'ETIMEDOUT',
        'ECONNRESET',
        'ECONNREFUSED',
        'EPIPE',
        'ERR_STREAM_DESTROYED',
        'rate_limit_exceeded',
        'temporary_failure'
    ];

    // If error has a retryable property, use that
    if (typeof error.retryable === 'boolean') {
        return error.retryable;
    }

    // Check error codes
    if (error.code && retryableCodes.includes(error.code)) {
        return true;
    }

    // Check error message for common retryable patterns
    const retryablePatterns = [
        /timeout/i,
        /temporarily unavailable/i,
        /rate limit/i,
        /too many requests/i,
        /try again/i,
        /connection (failed|reset)/i
    ];

    const errorMessage = error.message || '';
    return retryablePatterns.some(pattern => pattern.test(errorMessage));
}

// Sleep utility function
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// **TOOL RESULT TYPES**
type SearchResult = {
    error?: string;
    crawledContent?: Array<{
        crawled: boolean;
        extractedTitle?: string;
        title?: string;
        content?: string;
    }>;
    results?: Array<{
        title: string;
        snippet: string;
    }>;
    query?: string;
};

type CrawlResult = {
    success: boolean;
    title?: string;
    content?: string;
    error?: string;
};

type ReminderResult = {
    success: boolean;
    pendingCount?: number;
    reminders?: any[];
    message?: string;
};

// Type guards for tool results
function isSearchResult(tr: any): tr is SearchResult {
    return tr && typeof tr === 'object';
}

function isCrawlResult(tr: any): tr is CrawlResult {
    return tr && typeof tr === 'object' && 'success' in tr;
}

function isReminderResult(tr: any): tr is ReminderResult {
    return tr && typeof tr === 'object' && 'success' in tr;
}

function formatToolError(error: any): string {
    if (typeof error === 'string') return error;
    if (error instanceof Error) return error.message;
    if (typeof error === 'object' && 'message' in error) return String(error.message);
    return 'An unknown error occurred';
}

// **EMOJI FILTERING FUNCTIONS**
function removeEmojis(str: string): string {
    if (!str || typeof str !== 'string') return '';

    // Comprehensive regex for all emojis, including sequences and modifiers
    // Based on Unicode Emoji standards; covers single emojis, ZWJ sequences, etc.
    const emojiRegex = /[\u{1F000}-\u{1F9FF}\u{1F1E6}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{FE00}-\u{FE0F}\u{1F900}-\u{1F9FF}\u{1F300}-\u{1F5FF}]/gu;

    let cleaned = str
        // Remove only emojis using the comprehensive pattern
        .replace(emojiRegex, '')
        // Remove zero-width joiners used in emoji sequences
        .replace(/\u200D/g, '')
        // Clean up extra spaces while preserving numbers
        .replace(/\s+/g, ' ')
        .trim();

    // Safeguard: If filtered result is too short or empty, return original
    if (cleaned.length < 2 || cleaned.trim() === '') {
        return str.trim(); // Fallback to original
    }

    return cleaned;
}

function filterEmojiFromResponse(response: string): string {
    return removeEmojis(response);
}

// **INPUT FILTERING FUNCTIONS**
function sanitizeInput(input: string, allowSpecialChars: boolean = false): string {
    if (!input || typeof input !== 'string') return '';

    let sanitized = input.trim();

    if (!allowSpecialChars) {
        sanitized = sanitized.replace(/[%*^&<>{}[\]\\|`~]/g, '');
        sanitized = sanitized.replace(/\s+/g, ' ');
        sanitized = sanitized.replace(/^[^\w\s.,!?-]+|[^\w\s.,!?-]+$/g, '');
    }

    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    sanitized = sanitized.replace(/on\w+=/gi, '');

    return sanitized;
}

function validateAndSanitizeMessage(message: string): { isValid: boolean; sanitized: string; errors: string[] } {
    const errors: string[] = [];

    if (!message || typeof message !== 'string') {
        errors.push('Message must be a valid string');
        return { isValid: false, sanitized: '', errors };
    }

    if (message.length > 5000) {
        errors.push('Message exceeds maximum length of 5000 characters');
    }

    const suspiciousPatterns = [
        /(<script|<\/script>)/gi,
        /(javascript:|vbscript:)/gi,
        /(\bSELECT\b|\bUNION\b|\bDROP\b|\bDELETE\b|\bINSERT\b|\bUPDATE\b)/gi
    ];

    for (const pattern of suspiciousPatterns) {
        if (pattern.test(message)) {
            errors.push('Message contains potentially harmful content');
            break;
        }
    }

    const sanitized = sanitizeInput(message, true);

    if (sanitized.length === 0 && message.length > 0) {
        errors.push('Message became empty after sanitization');
    }

    return {
        isValid: errors.length === 0,
        sanitized,
        errors
    };
}

function sanitizeSearchQuery(query: string): string {
    if (!query) return '';
    let sanitized = query.trim();
    sanitized = sanitized.replace(/[<>{}[\]\\|`]/g, '');
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    return sanitized.substring(0, 200);
}

function sanitizeDatabaseInput(input: string): string {
    if (!input) return '';
    return sanitizeInput(input, false).substring(0, 1000);
}

// **WEB CRAWLING FUNCTIONS**
async function crawlWebPage(url: string): Promise<{ success: boolean; content?: string; error?: string; title?: string; }> {
    try {
        const urlObj = new URL(url);
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
            return { success: false, error: 'Invalid URL protocol' };
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000);

        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; NityashaBot/1.0; +https://nityasha.com/bot)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
        }

        const contentType = response.headers.get('content-type') || '';
        if (!contentType.includes('text/html')) {
            return { success: false, error: 'Not an HTML page' };
        }

        const html = await response.text();
        const dom = new JSDOM(html);
        const document = dom.window.document;

        const unwantedElements = document.querySelectorAll('script, style, nav, header, footer, aside, .advertisement, .ads, .sidebar');
        unwantedElements.forEach(element => element.remove());

        const title = document.querySelector('title')?.textContent?.trim() || '';

        let content = '';
        const contentSelectors = [
            'article',
            'main',
            '[role="main"]',
            '.content',
            '.main-content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '#content',
            '#main',
            '.container',
            'body'
        ];

        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                content = element.textContent || '';
                if (content.length > 200) break;
            }
        }

        content = content
            .replace(/\s+/g, ' ')
            .replace(/\n\s*\n/g, '\n')
            .trim();

        if (content.length > 10000) {
            content = content.substring(0, 10000) + '...';
        }

        const sanitizedContent = sanitizeInput(content, true);
        const sanitizedTitle = sanitizeInput(title, true);

        if (!sanitizedContent || sanitizedContent.length < 100) {
            return { success: false, error: 'No meaningful content extracted' };
        }

        return {
            success: true,
            content: sanitizedContent,
            title: sanitizedTitle
        };

    } catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown crawling error'
        };
    }
}

async function performGoogleSearchWithCrawling(searchQuery: string, crawlPages: boolean = true, maxCrawlPages: number = 3) {
    try {
        if (!searchQuery || typeof searchQuery !== 'string' || searchQuery.trim() === '' || searchQuery === 'undefined') {
            return {
                error: 'Invalid or empty search query provided',
                query: searchQuery,
                results: [],
                crawledContent: []
            };
        }

        const sanitizedQuery = sanitizeSearchQuery(searchQuery);

        if (!sanitizedQuery) {
            return {
                error: 'Search query became empty after sanitization',
                query: searchQuery,
                results: [],
                crawledContent: []
            };
        }

        const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
        const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;

        if (!apiKey || !searchEngineId) {
            return {
                error: 'Google Search API not configured',
                query: sanitizedQuery,
                results: [],
                crawledContent: []
            };
        }

        const response = await fetch(
            `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(sanitizedQuery)}&num=8`
        );

        if (!response.ok) {
            throw new Error(`Search API error: ${response.status}`);
        }

        const data = await response.json();

        const searchResults = data.items?.slice(0, 8).map((item: any) => ({
            title: sanitizeInput(item.title || '', true),
            snippet: sanitizeInput(item.snippet || '', true),
            link: item.link
        })) || [];

        if (!crawlPages || searchResults.length === 0) {
            return {
                query: sanitizedQuery,
                results: searchResults,
                totalResults: searchResults.length,
                crawledContent: []
            };
        }

        const crawlPromises = searchResults
            .slice(0, Math.min(maxCrawlPages, 5))
            .map(async (result) => {
                try {
                    const crawlResult = await crawlWebPage(result.link);
                    return {
                        ...result,
                        crawled: crawlResult.success,
                        fullContent: crawlResult.content || '',
                        crawlError: crawlResult.error,
                        extractedTitle: crawlResult.title || result.title
                    };
                } catch (error) {
                    return {
                        ...result,
                        crawled: false,
                        fullContent: '',
                        crawlError: 'Crawling failed',
                        extractedTitle: result.title
                    };
                }
            });

        const crawledResults = await Promise.all(crawlPromises);

        return {
            query: sanitizedQuery,
            results: searchResults,
            totalResults: searchResults.length,
            crawledContent: crawledResults,
            crawledSuccessfully: crawledResults.filter(r => r.crawled).length
        };

    } catch (error) {
        console.error('Google search with crawling error:', error);
        return {
            error: 'Search temporarily unavailable',
            query: searchQuery,
            results: [],
            crawledContent: []
        };
    }
}

// **REDIS AND UTILITY FUNCTIONS**
async function getRedisClient() {
    if (!redis) {
        redis = createClient({
            socket: {
                host: 'mercury.nityasha.com',
                port: 26739,
            },
            password: 'Amber@!23',
        });
        redis.on('error', (err: unknown) => {/* Redis error handling */ });
        await redis.connect();
    }
    return redis;
}

function historyKey(id: string) {
    return `chat_assistant:${sanitizeInput(id)}:messages`;
}

// **IMPROVED IST TIME FUNCTION**
function getCurrentISTTime(): string {
    try {
        const now = new Date();

        // Convert to IST using proper timezone handling
        const istTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));

        const year = istTime.getFullYear();
        const month = String(istTime.getMonth() + 1).padStart(2, '0');
        const day = String(istTime.getDate()).padStart(2, '0');
        const hours = String(istTime.getHours()).padStart(2, '0');
        const minutes = String(istTime.getMinutes()).padStart(2, '0');
        const seconds = String(istTime.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        // Fallback method
        const now = new Date();
        const istOffset = 5.5 * 60 * 60 * 1000;
        const istTime = new Date(now.getTime() + istOffset);

        return istTime.toISOString().slice(0, 19).replace('T', ' ');
    }
}

async function checkPendingReminders(userId: number) {
    try {
        const currentTime = getCurrentISTTime();
        const reminders = await query(
            `SELECT * FROM reminders 
             WHERE user_id = ? AND is_completed = 0 AND reminder_date <= ? 
             ORDER BY reminder_date ASC`,
            [userId, currentTime]
        ) as any[];
        return reminders;
    } catch (error) {
        return [];
    }
}

function normalizeMessage(message: any) {
    if (!message || !message.role) {
        return null;
    }

    if (typeof message.content === 'string') {
        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: message.content,
        };
    } else if (Array.isArray(message.content)) {
        const textContent = message.content
            .filter((item: any) => item && item.type === 'text')
            .map((item: any) => item.text)
            .join(' ');

        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: textContent || 'Empty message',
        };
    } else if (message.parts && Array.isArray(message.parts)) {
        const textContent = message.parts
            .filter((part: any) => part && part.type === 'text')
            .map((part: any) => part.text)
            .join(' ');

        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: textContent || 'Empty message',
        };
    }

    return {
        id: message.id || crypto.randomUUID(),
        role: message.role,
        content: String(message.content || message.parts || ''),
    };
}

async function loadHistory(id: string) {
    try {
        const client = await getRedisClient();
        const raw = await client.get(historyKey(id));
        const parsed = raw ? JSON.parse(raw) : [];

        if (!Array.isArray(parsed)) {
            return [];
        }

        return parsed.filter(msg => msg).map(normalizeMessage).filter(msg => msg !== null);
    } catch (error) {
        /* Failed to load history */
        return [];
    }
}

async function saveHistory(id: string, messages: any[]) {
    try {
        const client = await getRedisClient();
        const normalized = messages.map(normalizeMessage).filter(msg => msg !== null);
        await client.set(historyKey(id), JSON.stringify(normalized));
    } catch (error) {
        /* Failed to save history */
    }
}

// **USER LOCATION RETRIEVAL FUNCTION**
async function getUserLocation(userId: number): Promise<string> {
    try {
        const locations = await query(
            'SELECT location_data, updated_at FROM user_locations WHERE user_id = ?', 
            [userId]
        );
        
        if (!locations || locations.length === 0) {
            return 'Location: Not set (user can say "save my location" to set it)';
        }
        
        const locationRecord = locations[0];
        const locationData = JSON.parse(locationRecord.location_data);
        
        const city = locationData.city || 'Unknown city';
        const state = locationData.state || 'Unknown state';
        const country = locationData.country || 'Unknown country';
        const coords = locationData.latitude && locationData.longitude 
            ? `(${locationData.latitude}, ${locationData.longitude})` 
            : '';
        
        return `Location: ${city}, ${state}, ${country} ${coords}`.trim();
        
    } catch (error) {
        console.error('Error retrieving user location:', error);
        return 'Location: Unable to retrieve location data';
    }
}


async function clearHistory(id: string) {
    try {
        const client = await getRedisClient();
        await client.del(historyKey(id));
    } catch (error) {
        /* Failed to clear history */
    }
}

// **ENHANCED REMINDER FUNCTIONS**
function sanitizeReminderTitle(input: string): string {
    if (!input || typeof input !== 'string') return '';

    let sanitized = input.trim();

    // Only remove dangerous script-related content, keep most characters
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    sanitized = sanitized.replace(/on\w+=/gi, '');
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitized = sanitized.replace(/\s+/g, ' '); // Normalize spaces

    return sanitized.substring(0, 255); // Limit length for database
}

function sanitizeReminderDescription(input: string): string {
    if (!input || typeof input !== 'string') return '';

    let sanitized = input.trim();

    // Allow more characters in description but remove dangerous content
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    sanitized = sanitized.replace(/on\w+=/gi, '');
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitized = sanitized.replace(/\s+/g, ' ');

    return sanitized.substring(0, 1000); // Limit length for database
}

// **ENHANCED NATURAL DATE PARSING WITH HINDI/HINGLISH SUPPORT**
function parseNaturalDate(dateString: string): string | null {
    try {
        if (!dateString || typeof dateString !== 'string') {
            return null;
        }

        const cleanDateString = dateString.trim().toLowerCase();

        if (!cleanDateString) {
            return null;
        }

        // Current IST time
        const now = new Date();
        const istOffset = 5.5 * 60 * 60 * 1000; // IST offset in milliseconds
        const istNow = new Date(now.getTime() + istOffset);

        let parsedDate: Date | null = null;

        // Handle Hindi/Hinglish terms
        const hindiTerms: { [key: string]: number } = {
            'आज': 0,
            'कल': 1,
            'परसों': 2,
            'today': 0,
            'tomorrow': 1,
            'day after tomorrow': 2,
            'kal': 1,
            'parso': 2,
            'parson': 2,
            'अगले हफ्ते': 7,
            'next week': 7,
            'अगले महीने': 30,
            'next month': 30
        };

        // Time expressions mapping
        const timeExpressions: { [key: string]: number } = {
            'सुबह': 8,
            'दोपहर': 12,
            'शाम': 18,
            'रात': 20,
            'morning': 8,
            'afternoon': 14,
            'evening': 18,
            'night': 20,
            'noon': 12,
            'midnight': 0
        };

        // Check for relative date terms first
        for (const [term, days] of Object.entries(hindiTerms)) {
            if (cleanDateString.includes(term)) {
                parsedDate = new Date(istNow);
                parsedDate.setDate(parsedDate.getDate() + days);
                break;
            }
        }

        // If we found a relative date, now look for time
        if (parsedDate) {
            // Look for specific time like "5 बजे", "9 AM", "5 PM"
            const timeMatch = cleanDateString.match(/(\d{1,2})\s*(?:बजे|am|pm|:\d{2})/i);
            if (timeMatch) {
                let hour = parseInt(timeMatch[1]);
                if (cleanDateString.includes('pm') && hour !== 12) {
                    hour += 12;
                } else if (cleanDateString.includes('am') && hour === 12) {
                    hour = 0;
                }
                parsedDate.setHours(hour, 0, 0, 0);
            } else {
                // Look for time expressions
                let timeSet = false;
                for (const [expression, hour] of Object.entries(timeExpressions)) {
                    if (cleanDateString.includes(expression)) {
                        parsedDate.setHours(hour, 0, 0, 0);
                        timeSet = true;
                        break;
                    }
                }

                // If no specific time found, use default 9 AM
                if (!timeSet) {
                    parsedDate.setHours(9, 0, 0, 0);
                }
            }
        }

        // If no relative date found, try standard parsing
        if (!parsedDate) {
            // Try to parse as standard date
            parsedDate = new Date(cleanDateString);

            // If that fails, try common formats
            if (isNaN(parsedDate.getTime())) {
                const formats = [
                    cleanDateString.replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/, '$3-$2-$1'), // DD/MM/YYYY to YYYY-MM-DD
                    cleanDateString.replace(/(\d{1,2})-(\d{1,2})-(\d{4})/, '$3-$2-$1'), // DD-MM-YYYY to YYYY-MM-DD
                    cleanDateString.replace(/(\d{1,2})\.(\d{1,2})\.(\d{4})/, '$3-$2-$1'), // DD.MM.YYYY to YYYY-MM-DD
                ];

                for (const format of formats) {
                    parsedDate = new Date(format);
                    if (!isNaN(parsedDate.getTime())) break;
                }
            }

            // If still no valid date and no time was set, set default time
            if (parsedDate && !isNaN(parsedDate.getTime()) && parsedDate.getHours() === 0 && parsedDate.getMinutes() === 0) {
                parsedDate.setHours(9, 0, 0, 0);
            }
        }

        // If still invalid, return null
        if (!parsedDate || isNaN(parsedDate.getTime())) {
            return null;
        }

        // Ensure future date
        if (parsedDate <= istNow) {
            parsedDate.setDate(parsedDate.getDate() + 1);
        }

        // Format to YYYY-MM-DD HH:MM:SS
        const year = parsedDate.getFullYear();
        const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
        const day = String(parsedDate.getDate()).padStart(2, '0');
        const hours = String(parsedDate.getHours()).padStart(2, '0');
        const minutes = String(parsedDate.getMinutes()).padStart(2, '0');
        const seconds = String(parsedDate.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    } catch (error) {
        console.error('Date parsing error:', error);
        return null;
    }
}

async function googleImageSearch(query: string, numResults: number = 6) {
    const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
    const cx = process.env.GOOGLE_SEARCH_ENGINE_ID;
    if (!apiKey || !cx) {
        throw new Error('Missing Google API key or CSE ID');
    }
    const url = 'https://www.googleapis.com/customsearch/v1';
    try {
        const response = await axios.get(url, {
            params: {
                key: apiKey,
                cx: cx,
                q: query,
                num: numResults,
                searchType: 'image',
                safe: 'active',
                imgSize: 'medium',
                imgType: 'photo',
            },
        });

        const items = response.data.items || [];
        return items.map((item: any) => ({
            title: item.title || 'Untitled Image',
            imageUrl: item.link,
            sourceUrl: item.image?.contextLink || item.displayLink || item.link,
        }));
    } catch (error) {
        console.error('Google Image Search error:', error);
        return [];
    }
}


// **ENHANCED TOOLS WITH BETTER DATE HANDLING**
function createToolsWithUserId(currentUserId: number) {
    return {
        google_search: tool({
            description: 'Search Google for current information and crawl web pages for detailed content. Use this tool whenever the user asks about news, current events, weather, recent information, or any topic that requires up-to-date data from the web.',
            parameters: z.object({
                query: z.string()
                    .min(1, "Search query cannot be empty")
                    .refine(val => val !== 'undefined' && val.trim() !== '', {
                        message: "Search query must be a valid non-empty string"
                    })
                    .describe('The search query based on what the user is asking for. Extract keywords from the user message. For example: if user says "latest news of india" use "latest news india 2025", if user asks about weather use "weather [location] today"'),
                crawl_pages: z.boolean().optional().default(true).describe('Whether to crawl and extract full content from top search results'),
                max_crawl_pages: z.number().min(1).max(5).optional().default(3).describe('Maximum number of pages to crawl (1-5)')
            }),
            execute: async ({ query, crawl_pages, max_crawl_pages }) => {

                if (!query || typeof query !== 'string' || query.trim() === '' || query === 'undefined') {
                    console.error('❌ Invalid search query received in tool execution:', {
                        query,
                        type: typeof query,
                        trimmed: query?.trim?.(),
                        length: query?.length
                    });
                    return {
                        error: 'Invalid search query provided to tool',
                        query: query,
                        results: [],
                        crawledContent: []
                    };
                }

                const sanitizedQuery = sanitizeSearchQuery(query);
                const limitedCrawlPages = Math.min(Math.max(max_crawl_pages || 3, 1), 5);
                const result = await performGoogleSearchWithCrawling(sanitizedQuery, crawl_pages, limitedCrawlPages);
                return result;
            },
        }),

        crawl_website: tool({
            description: 'Crawl a specific website URL to extract its full content. Use this when you have a specific URL to analyze.',
            parameters: z.object({
                url: z.string().url("Must be a valid URL").describe('The URL to crawl and extract content from'),
            }),
            execute: async ({ url }) => {
                const sanitizedUrl = sanitizeInput(url, true);
                if (!sanitizedUrl.startsWith('http://') && !sanitizedUrl.startsWith('https://')) {
                    return { success: false, error: 'Invalid URL format' };
                }
                const result = await crawlWebPage(sanitizedUrl);
                return result;
            },
        }),

        get_current_weather: tool({
            description: 'Get current weather for a location',
            parameters: z.object({
                location: z.string().min(1, "Location cannot be empty").describe('City and region/country'),
                unit: z.enum(['celsius', 'fahrenheit']).optional().default('celsius'),
            }),
            execute: async ({ location, unit }) => {
                const sanitizedLocation = sanitizeInput(location, true);
                const temperature = unit === 'fahrenheit' ? 86 : 30;
                return {
                    location: sanitizedLocation,
                    unit,
                    temperature,
                    conditions: 'Sunny',
                    humidity: '65%',
                    windSpeed: '10 km/h'
                };
            },
        }),

        check_pending_reminders: tool({
            description: 'Check for pending reminders that need user attention',
            parameters: z.object({}),
            execute: async () => {
                try {
                    const pendingReminders = await checkPendingReminders(currentUserId);
                    return {
                        success: true,
                        pendingCount: pendingReminders.length,
                        reminders: pendingReminders
                    };
                } catch (error) {
                    return { success: false, message: 'Failed to check reminders' };
                }
            },
        }),

        get_location: tool({
    description: 'Get the saved location data for the current user from the database',
    parameters: z.object({}),
    execute: async () => {
        try {
            const locations = await query(
                'SELECT location_data, created_at, updated_at FROM user_locations WHERE user_id = ?', 
                [currentUserId]
            );
            
            if (!locations || locations.length === 0) {
                return { 
                    success: false, 
                    message: 'No saved location found for this user' 
                };
            }
            
            // Get the user's location record
            const locationRecord = locations[0];
            
            // Parse the JSON location data
            const locationData = JSON.parse(locationRecord.location_data);
            
            return {
                success: true,
                data: {
                    location_data: locationData,
                    created_at: locationRecord.created_at,
                    updated_at: locationRecord.updated_at
                },
                message: `Location retrieved: ${locationData.city || 'Unknown city'}, ${locationData.state || 'Unknown state'}`
            };
            
        } catch (error) {
            console.error('Get location error:', error);
            return { 
                success: false, 
                message: 'Failed to retrieve location data from database' 
            };
        }
    },
}),
     
        // **ENHANCED ADD REMINDER TOOL**
        add_reminder: tool({
            description: 'Add a new reminder for the user. Accepts natural language dates in Hindi/English like "कल सुबह", "tomorrow 9 AM", "आज शाम 6 बजे".',
            parameters: z.object({
                title: z.string().min(1, "Title cannot be empty").describe('Reminder title'),
                description: z.string().optional().describe('Reminder description'),
                reminder_date: z.string().describe('Date and time in natural language (e.g., "कल 5 बजे", "tomorrow 9 AM", "आज शाम", "परसों दोपहर")')
            }),
            execute: async ({ title, description, reminder_date }) => {
                try {
                    // Clean and validate inputs
                    const cleanTitle = sanitizeReminderTitle(title);
                    const cleanDescription = sanitizeReminderDescription(description || '');

                    if (!cleanTitle) {
                        return {
                            success: false,
                            message: 'Invalid title provided'
                        };
                    }

                    // Parse the date
                    let formattedDate = parseNaturalDate(reminder_date);

                    if (!formattedDate) {
                        // If parsing completely fails, return helpful error message
                        return {
                            success: false,
                            message: 'मैं date/time समझ नहीं पाया। कृपया ऐसे बोलें: "कल सुबह 9 बजे" या "tomorrow 5 PM" या "आज शाम 6 बजे"'
                        };
                    }


                    // Add to database
                    const result = await query(
                        'INSERT INTO reminders (user_id, title, description, reminder_date, created_at) VALUES (?, ?, ?, ?, ?)',
                        [currentUserId, cleanTitle, cleanDescription, formattedDate, getCurrentISTTime()]
                    ) as any;

                    return {
                        success: true,
                        reminder_id: result.insertId,
                        message: `रिमाइंडर "${cleanTitle}" सेट हो गया ${formattedDate} के लिए`,
                        scheduled_time: formattedDate,
                        title: cleanTitle
                    };

                } catch (error) {
                    console.error('Add reminder error:', error);
                    return {
                        success: false,
                        message: 'रिमाइंडर बनाने में समस्या हुई। कृपया दोबारा कोशिश करें।'
                    };
                }
            },
        }),

        get_reminders: tool({
            description: 'Get all reminders for the user',
            parameters: z.object({
                completed: z.boolean().optional().describe('Filter by completion status'),
                upcoming_only: z.boolean().optional().describe('Show only upcoming reminders'),
            }),
            execute: async ({ completed, upcoming_only }) => {
                try {
                    let sql = 'SELECT * FROM reminders WHERE user_id = ?';
                    const params: any[] = [currentUserId];

                    if (completed !== undefined) {
                        sql += ' AND is_completed = ?';
                        params.push(completed);
                    }

                    if (upcoming_only) {
                        sql += ' AND reminder_date > ?';
                        params.push(getCurrentISTTime());
                    }

                    sql += ' ORDER BY reminder_date ASC';

                    const reminders = await query(sql, params);
                    return {
                        success: true,
                        reminders,
                        currentTime: getCurrentISTTime()
                    };
                } catch (error) {
                    return { success: false, message: 'Failed to get reminders' };
                }
            },
        }),

        send_email: tool({
    description: 'Prepare email with subject and body content (returns formatted email data without sending)',
    parameters: z.object({
        subject: z.string().min(1, "Subject cannot be empty").describe('Email subject line'),
        body: z.string().min(1, "Body cannot be empty").describe('Email body content'),
        to_email: z.string().email("Must be a valid email address").optional().describe('Recipient email (optional)'),
    }),
    execute: async ({ subject, body, to_email }) => {
        try {
            // Sanitize inputs
            const sanitizedSubject = sanitizeInput(subject, true);
            const sanitizedBody = sanitizeInput(body, true);
            const sanitizedToEmail = to_email ? sanitizeInput(to_email, true) : null;

            if (!sanitizedSubject || !sanitizedBody) {
                return {
                    success: false,
                    message: 'Invalid subject or body after sanitization'
                };
            }

            // Just return the formatted email data
            return {
                success: true,
                email_data: {
                    subject: sanitizedSubject,
                    body: sanitizedBody,
                    to_email: sanitizedToEmail,
                    created_at: getCurrentISTTime(),
                    user_id: currentUserId
                },
                message: `Email prepared successfully - Subject: "${sanitizedSubject}"`
            };

        } catch (error) {
            console.error('Email preparation error:', error);
            return {
                success: false,
                message: 'Failed to prepare email data'
            };
        }
    },
}),

        // Add this native alarm tool to createToolsWithUserId function

        update_reminder: tool({
            description: 'Update a reminder',
            parameters: z.object({
                reminder_id: z.number().positive("Reminder ID must be positive").describe('Reminder ID to update'),
                title: z.string().optional().describe('New title'),
                description: z.string().optional().describe('New description'),
                reminder_date: z.string().optional().describe('New reminder date in natural language'),
                is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
            }),
            execute: async ({ reminder_id, title, description, reminder_date, is_completed }) => {
                try {
                    const updates: string[] = [];
                    const params: any[] = [];

                    if (title) {
                        const sanitizedTitle = sanitizeReminderTitle(title);
                        if (sanitizedTitle) {
                            updates.push('title = ?');
                            params.push(sanitizedTitle);
                        }
                    }
                    if (description !== undefined) {
                        updates.push('description = ?');
                        params.push(sanitizeReminderDescription(description));
                    }
                    if (reminder_date) {
                        const parsedDate = parseNaturalDate(reminder_date);
                        if (parsedDate) {
                            updates.push('reminder_date = ?');
                            params.push(parsedDate);
                        }
                    }
                    if (is_completed !== undefined) {
                        updates.push('is_completed = ?');
                        params.push(is_completed);
                    }

                    if (updates.length === 0) {
                        return { success: false, message: 'No valid fields to update' };
                    }

                    params.push(reminder_id, currentUserId);

                    await query(
                        `UPDATE reminders SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
                        params
                    );

                    return { success: true, reminder_id };
                } catch (error) {
                    return { success: false, message: 'Failed to update reminder' };
                }
            },
        }),
        
                suggest_products: tool({
            description:
                'Comprehensive product search tool that suggests top 3 products with descriptions using Flipkart API, Google search, and multi-retry logic until quality products are found.',
            parameters: z.object({
                query: z.string().describe(
                    'The product suggestion query, e.g., type, budget, or category.'
                ),
                numResults: z
                    .number()
                    .optional()
                    .default(3)
                    .describe('Number of products to return (default: 3, max: 5).'),
                includeGoogleResults: z
                    .boolean()
                    .optional()
                    .default(true)
                    .describe(
                        'Include Google search results along with Flipkart (default: true).'
                    )
            }),
            execute: async ({
                query,
                numResults = 3,
                includeGoogleResults = true
            }) => {
                /* ------------------------------------------------------------------ *
                 * 1. GUARD AGAINST EMPTY / INVALID QUERY                             *
                 * ------------------------------------------------------------------ */
                if (!query || typeof query !== 'string' || query.trim() === '') {
                    return {
                        error: 'Product-search query must be a non-empty string',
                        results: [],
                        searchMetadata: { receivedQuery: query }
                    };
                }

                try {
                    console.log('🔍 Comprehensive Product Search Started:', query);

                    /* ------------------------------ *
                     * 2.  HELPERS (UNCHANGED)        *
                     * ------------------------------ */
                    const affiliateId =
                        process.env.FLIPKART_AFFILIATE_ID || '6e80384ac6764f619e4ccab4819d3bf1';
                    const affiliateToken = process.env.FLIPKART_AFFILIATE_TOKEN;
                    const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
                    const cx = process.env.GOOGLE_SEARCH_ENGINE_ID;

                    const safeFormatPrice = (price: any): string | undefined => {
                        if (price === null || price === undefined) return undefined;
                        const num = typeof price === 'number'
                            ? price
                            : parseFloat(String(price).replace(/[^\d.]/g, ''));
                        return isNaN(num) || num <= 0 ? undefined : `₹${num.toLocaleString('en-IN')}`;
                    };

                    const createFlipkartAffiliateLink = (url: string): string =>
                        url.includes('flipkart.com')
                            ? `${url}${url.includes('?') ? '&' : '?'}affid=${affiliateId}&affExtParam1=zola_chat`
                            : url;

                    const extractPriceInfo = (snippet: string): {
                        price?: string;
                        originalPrice?: string;
                        discount?: string;
                        rating?: string;
                    } => {
                        const priceRegex = /₹[\d,]+|Rs\.?\s*[\d,]+|\$[\d,]+|INR\s*[\d,]+/gi;
                        const discountRegex = /(\d+)%\s*(off|discount)/i;
                        const ratingRegex = /(\d+\.?\d*)\s*(?:stars?|rating|⭐)/i;

                        const prices = snippet.match(priceRegex) || [];
                        const discountMatch = snippet.match(discountRegex);
                        const ratingMatch = snippet.match(ratingRegex);

                        return {
                            price: prices[0],
                            originalPrice: prices.length > 1 ? prices[1] : undefined,
                            discount: discountMatch ? `${discountMatch[1]}% OFF` : undefined,
                            rating: ratingMatch ? `${ratingMatch[1]}/5` : undefined
                        };
                    };

                    const flipkartProductSearch = async (
                        q: string,
                        limit = 5
                    ): Promise<any[]> => {
                        if (!affiliateToken) return [];
                        try {
                            const { data } = await axios.get(
                                'https://affiliate-api.flipkart.net/affiliate/1.0/search.json',
                                {
                                    params: { query: q, resultCount: limit },
                                    headers: {
                                        'Fk-Affiliate-Id': affiliateId,
                                        'Fk-Affiliate-Token': affiliateToken
                                    }
                                }
                            );
                            const products = data?.products || [];
                            return products.map((p: any) => {
                                const b = p.productBaseInfoV1;
                                return {
                                    name: b?.title || 'Unknown Product',
                                    description: b?.productDescription || '',
                                    link: b?.productUrl || '',
                                    flipkartSpecialPrice: b?.flipkartSpecialPrice,
                                    flipkartSellingPrice: b?.flipkartSellingPrice,
                                    maximumRetailPrice: b?.maximumRetailPrice,
                                    attributes: b?.attributes,
                                    offers: b?.offers,
                                    codAvailable: b?.codAvailable,
                                    productBrand: b?.productBrand,
                                    productShippingInfoV1: b?.productShippingInfoV1,
                                    
                                    image:
                                        b?.imageUrls?.['400x400'] ||
                                        b?.imageUrls?.['200x200'] ||
                                        null,
                                    price: safeFormatPrice(b?.flipkartSellingPrice),
                                    originalPrice: safeFormatPrice(b?.flipkartSpecialPrice),
                                    discount: b?.discountPercentage
                                        ? `${b.discountPercentage}% OFF`
                                        : undefined,
                                    rating: b?.productRating?.average
                                        ? `${b.productRating.average}/5`
                                        : undefined,
                                    platform: 'Flipkart',
                                    affiliateLink: createFlipkartAffiliateLink(b?.productUrl || '')
                                };
                            });
                        } catch (err) {
                            console.error('Flipkart API error:', err);
                            return [];
                        }
                    };

                    const getGoogleProductResults = async (
                        q: string,
                        limit = 6
                    ): Promise<any[]> => {
                        if (!includeGoogleResults || !apiKey || !cx) return [];
                        try {
                            const { data } = await axios.get(
                                'https://www.googleapis.com/customsearch/v1',
                                {
                                    params: {
                                        key: apiKey,
                                        cx,
                                        q: `${q} buy online price`,
                                        num: limit
                                    }
                                }
                            );
                            const items = data.items || [];
                            return items.map((item: any) => {
                                const priceInfo = extractPriceInfo(item.snippet || '');
                                const platform = item.link.includes('flipkart.com')
                                    ? 'Flipkart'
                                    : item.link.includes('amazon.')
                                        ? 'Amazon'
                                        : 'Other';
                                return {
                                    name: item.title || 'Unknown Product',
                                    description: item.snippet || 'No description available',
                                    link: item.link,
                                    image: null,
                                    price: priceInfo.price,
                                    originalPrice: priceInfo.originalPrice,
                                    discount: priceInfo.discount,
                                    rating: priceInfo.rating,
                                    platform,
                                    affiliateLink:
                                        platform === 'Flipkart'
                                            ? createFlipkartAffiliateLink(item.link)
                                            : item.link
                                };
                            });
                        } catch (err) {
                            console.error('Google Product Search error:', err);
                            return [];
                        }
                    };

                    /* ------------------------------ *
                     * 3. SIMPLIFIED QUERY GENERATOR   *
                     * ------------------------------ */
                    const generateSearchQueries = (baseQuery: string): string[] => {
                        const currentYear = new Date().getFullYear();
                        const queries = [];

                        // Simple query generation without complex conditionals
                        queries.push(baseQuery);
                        queries.push(`best ${baseQuery} ${currentYear} top rated`);
                        queries.push(`${baseQuery} latest model ${currentYear} with features`);

                        // Remove duplicates and return
                        return [...new Set(queries)];
                    };

                    /* ------------------------------ *
                     * 4.  SEARCH & RETRY LOGIC       *
                     * ------------------------------ */
                    let allResults: any[] = [];
                    let searchCount = 0;
                    let validFound = 0;
                    const searchBreakdown: Record<string, number> = {};
                    const target = Math.min(numResults, 5);
                    const maxSearches = 6; // Reduced since we have fewer queries

                    // Generate all queries dynamically
                    const allQueries = generateSearchQueries(query);
                    const usedQueries: string[] = [];

                    const hasDescription = (p: any) =>
                        p.description &&
                        p.description.trim().length > 20 &&
                        !p.description.includes('Description not found');

                    const uniqueWithDescription = (arr: any[]) => {
                        const map = new Map<string, any>();
                        arr.filter(hasDescription).forEach(p => {
                            const key = p.link || p.name.toLowerCase().replace(/\s+/g, ' ').trim();
                            if (!map.has(key)) map.set(key, p);
                        });
                        return [...map.values()];
                    };

                    const runSearch = async (q: string, label: string) => {
                        searchCount++;
                        usedQueries.push(q);
                        const flip = await flipkartProductSearch(q, 10);
                        const goog = await getGoogleProductResults(q, Math.max(2, 8 - flip.length));
                        const combined = [...flip, ...goog].map(p => ({
                            ...p,
                            searchRound: searchCount,
                            searchQuery: q,
                            label
                        }));
                        allResults = allResults.concat(combined);
                        searchBreakdown[label] = combined.length;
                        validFound = uniqueWithDescription(allResults).length;
                    };

                    // Execute searches with simplified queries
                    for (let i = 0; i < allQueries.length && searchCount < maxSearches; i++) {
                        const label = `search_${searchCount + 1}`;
                        await runSearch(allQueries[i], label);

                        // Early exit if we have enough quality results
                        if (validFound >= target) break;
                    }

                    /* ------------------------------ *
                     * 5.  FILTER, SORT, RETURN       *
                     * ------------------------------ */
                    const withDesc = uniqueWithDescription(allResults);
                    if (withDesc.length === 0) {
                        return {
                            results: [],
                            error:
                                'No products with descriptions found after search. Please try a different query.',
                            searchMetadata: {
                                totalSearches: searchCount,
                                totalProductsScanned: allResults.length,
                                productsWithDescriptions: 0,
                                dynamicQueriesGenerated: allQueries.length,
                                queriesUsed: usedQueries
                            }
                        };
                    }

                    // Sort: longer descriptions + better discount + lower price
                    withDesc.sort((a, b) => {
                        const dLenA = a.description.length;
                        const dLenB = b.description.length;
                        const discountA =
                            a.price &&
                            a.originalPrice &&
                            parseFloat(a.price.replace(/[^\d.]/g, '')) <
                            parseFloat(a.originalPrice.replace(/[^\d.]/g, ''));
                        const discountB =
                            b.price &&
                            b.originalPrice &&
                            parseFloat(b.price.replace(/[^\d.]/g, '')) <
                            parseFloat(b.originalPrice.replace(/[^\d.]/g, ''));
                        if (dLenA > 50 && dLenB > 50) {
                            if (discountA && !discountB) return -1;
                            if (!discountA && discountB) return 1;
                        }
                        if (Math.abs(dLenA - dLenB) > 50) return dLenB - dLenA;
                        const priceA = parseFloat(a.price?.replace(/[^\d.]/g, '') || '999999');
                        const priceB = parseFloat(b.price?.replace(/[^\d.]/g, '') || '999999');
                        return priceA - priceB;
                    });

                    const top = withDesc.slice(0, target);

                    return {
                        results: top,
                        searchMetadata: {
                            totalSearches: searchCount,
                            totalProductsScanned: allResults.length,
                            productsWithDescriptions: withDesc.length,
                            finalSelections: top.length,

                            dynamicQueriesGenerated: allQueries.length,
                            queriesUsed: usedQueries,
                            searchBreakdown,
                            googleResultsIncluded: includeGoogleResults,
                            platformBreakdown: top.reduce((acc: any, p: any) => {
                                acc[p.platform] = (acc[p.platform] || 0) + 1;
                                return acc;
                            }, {})
                        }
                    };
                } catch (err: any) {
                    console.error('❌ Comprehensive Product Search Error:', err);
                    return {
                        error: 'Comprehensive product search failed. Please try again.',
                        results: [],
                        searchMetadata: {
                            errorOccurred: true,
                            errorMessage: err.message
                        }
                    };
                }
            }
        }),
        
        delete_reminder: tool({
            description: 'Delete a reminder',
            parameters: z.object({
                reminder_id: z.number().positive("Reminder ID must be positive").describe('Reminder ID to delete'),
            }),
            execute: async ({ reminder_id }) => {
                try {
                    await query('DELETE FROM reminders WHERE id = ? AND user_id = ?', [reminder_id, currentUserId]);
                    return { success: true, reminder_id };
                } catch (error) {
                    return { success: false, message: 'Failed to delete reminder' };
                }
            },
        }),

        // Todo management tools (keeping existing implementations)
        add_todo_list: tool({
            description: 'Create a new todo list for the user',
            parameters: z.object({
                name: z.string().min(1, "Name cannot be empty").describe('Todo list name'),
                description: z.string().optional().describe('Todo list description'),
            }),
            execute: async ({ name, description }) => {
                try {
                    const sanitizedName = sanitizeDatabaseInput(name);
                    const sanitizedDescription = sanitizeDatabaseInput(description || '');

                    if (!sanitizedName) {
                        return { success: false, message: 'Invalid name after sanitization' };
                    }

                    const result = await query(
                        'INSERT INTO todo_lists (user_id, name, description) VALUES (?, ?, ?)',
                        [currentUserId, sanitizedName, sanitizedDescription]
                    ) as any;
                    return { success: true, id: result.insertId, name: sanitizedName };
                } catch (error) {
                    return { success: false, message: 'Failed to create todo list' };
                }
            },
        }),

        get_todo_lists: tool({
            description: 'Get all todo lists for the user',
            parameters: z.object({}),
            execute: async () => {
                try {
                    const lists = await query('SELECT * FROM todo_lists WHERE user_id = ? ORDER BY created_at DESC', [currentUserId]);
                    return { success: true, lists };
                } catch (error) {
                    return { success: false, message: 'Failed to get todo lists' };
                }
            },
        }),

        add_todo_item: tool({
            description: 'Add a new todo item to a list',
            parameters: z.object({
                list_id: z.number().optional().describe('Todo list ID (optional)'),
                title: z.string().min(1, "Title cannot be empty").describe('Todo item title'),
                description: z.string().optional().describe('Todo item description'),
                priority: z.enum(['low', 'medium', 'high']).optional().default('medium'),
                due_date: z.string().optional().describe('Due date in natural language'),
            }),
            execute: async ({ list_id, title, description, priority, due_date }) => {
                try {
                    const sanitizedTitle = sanitizeDatabaseInput(title);
                    const sanitizedDescription = sanitizeDatabaseInput(description || '');
                    let sanitizedDueDate = null;

                    if (due_date) {
                        sanitizedDueDate = parseNaturalDate(due_date);
                    }

                    if (!sanitizedTitle) {
                        return { success: false, message: 'Invalid title after sanitization' };
                    }

                    const result = await query(
                        'INSERT INTO todo_items (user_id, list_id, title, description, priority, due_date) VALUES (?, ?, ?, ?, ?, ?)',
                        [currentUserId, list_id || null, sanitizedTitle, sanitizedDescription, priority, sanitizedDueDate]
                    ) as any;
                    return { success: true, id: result.insertId, title: sanitizedTitle };
                } catch (error) {
                    return { success: false, message: 'Failed to add todo item' };
                }
            },
        }),

        get_todo_items: tool({
            description: 'Get todo items for the user',
            parameters: z.object({
                list_id: z.number().optional().describe('Filter by specific todo list ID'),
                completed: z.boolean().optional().describe('Filter by completion status'),
            }),
            execute: async ({ list_id, completed }) => {
                try {
                    let sql = `
                        SELECT ti.*, tl.name as list_name
                        FROM todo_items ti
                        LEFT JOIN todo_lists tl ON ti.list_id = tl.id
                        WHERE ti.user_id = ?
                    `;
                    const params: any[] = [currentUserId];

                    if (list_id !== undefined) {
                        sql += ' AND ti.list_id = ?';
                        params.push(list_id);
                    }

                    if (completed !== undefined) {
                        sql += ' AND ti.is_completed = ?';
                        params.push(completed);
                    }

                    sql += ' ORDER BY ti.due_date ASC, ti.priority DESC, ti.created_at DESC';

                    const items = await query(sql, params);
                    return { success: true, items };
                } catch (error) {
                    return { success: false, message: 'Failed to get todo items' };
                }
            },
        }),

        update_todo_item: tool({
            description: 'Update a todo item',
            parameters: z.object({
                item_id: z.number().positive("Item ID must be positive").describe('Todo item ID to update'),
                title: z.string().optional().describe('New title'),
                description: z.string().optional().describe('New description'),
                priority: z.enum(['low', 'medium', 'high']).optional().describe('New priority'),
                due_date: z.string().optional().describe('New due date in natural language'),
                is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
            }),
            execute: async ({ item_id, title, description, priority, due_date, is_completed }) => {
                try {
                    const updates: string[] = [];
                    const params: any[] = [];

                    if (title) {
                        const sanitizedTitle = sanitizeDatabaseInput(title);
                        if (sanitizedTitle) {
                            updates.push('title = ?');
                            params.push(sanitizedTitle);
                        }
                    }
                    if (description !== undefined) {
                        updates.push('description = ?');
                        params.push(sanitizeDatabaseInput(description));
                    }
                    if (priority) {
                        updates.push('priority = ?');
                        params.push(priority);
                    }
                    if (due_date !== undefined) {
                        let parsedDueDate = null;
                        if (due_date) {
                            parsedDueDate = parseNaturalDate(due_date);
                        }
                        updates.push('due_date = ?');
                        params.push(parsedDueDate);
                    }
                    if (is_completed !== undefined) {
                        updates.push('is_completed = ?');
                        params.push(is_completed);
                    }

                    if (updates.length === 0) {
                        return { success: false, message: 'No valid fields to update' };
                    }

                    params.push(item_id, currentUserId);

                    await query(
                        `UPDATE todo_items SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
                        params
                    );

                    return { success: true, item_id };
                } catch (error) {
                    return { success: false, message: 'Failed to update todo item' };
                }
            },
        }),

        delete_todo_item: tool({
            description: 'Delete a todo item',
            parameters: z.object({
                item_id: z.number().positive("Item ID must be positive").describe('Todo item ID to delete'),
            }),
            execute: async ({ item_id }) => {
                try {
                    await query('DELETE FROM todo_items WHERE id = ? AND user_id = ?', [item_id, currentUserId]);
                    return { success: true, item_id };
                } catch (error) {
                    return { success: false, message: 'Failed to delete todo item' };
                }
            },
        }),
    };
}

// **MAIN API HANDLER WITH ENHANCED DATE SUPPORT**
export async function POST(req: NextRequest) {
    try {
        let toolCalls: any[] = [];
        let toolResults: any[] = [];
        let toolUsage: any = {};

        // **Better JSON parsing**
        let body;
        try {
            const rawBody = await req.text();

            if (!rawBody || rawBody.trim() === '') {
                throw new Error('Empty request body');
            }

            body = JSON.parse(rawBody);
        } catch (parseError) {
            console.error('❌ JSON parsing error:', parseError);
            return new Response(
                JSON.stringify({
                    error: 'Invalid JSON in request body',
                    details: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const { user_id, message, filter_emojis }: { user_id: number; message: string; filter_emojis?: boolean } = body || {};

        if (!user_id || typeof user_id !== 'number' || user_id <= 0) {
            return new Response(
                JSON.stringify({
                    error: 'Invalid user_id: must be a positive number',
                    received: { user_id, type: typeof user_id }
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        if (!message || typeof message !== 'string') {
            return new Response(
                JSON.stringify({
                    error: 'Invalid message: must be a non-empty string',
                    received: { message: message, type: typeof message }
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const messageValidation = validateAndSanitizeMessage(message);

        if (!messageValidation.isValid) {
            return new Response(
                JSON.stringify({
                    error: 'Invalid message content',
                    details: messageValidation.errors
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const sanitizedMessage = messageValidation.sanitized;

        // Load chat history
        const id = `chat_${user_id}`;
        const prior = await loadHistory(id);

        const newMessage = {
            id: crypto.randomUUID(),
            role: 'user' as const,
            content: sanitizedMessage,
        };

        let allMessages = [...(prior || []), newMessage];

        // Check for corrupted data
        const hasCorruptedData = (prior || []).some((msg: any) =>
            msg &&
            msg.role === 'assistant' &&
            msg.content &&
            typeof msg.content === 'string' &&
            msg.content.includes('[object Object]')
        );

        if (hasCorruptedData) {
            await clearHistory(id);
            allMessages = [newMessage];
        }

        // Normalize messages
        const validMessages = allMessages
            .filter(msg => msg && msg.role && msg.content)
            .map(msg => normalizeMessage(msg))
            .filter(msg => msg !== null);


        // Create tools
        const tools = createToolsWithUserId(user_id);
        const userLocationInfo = await getUserLocation(user_id);

        // Check for pending reminders
        const pendingReminders = await checkPendingReminders(user_id);
        let reminderContext = '';

        if (pendingReminders.length > 0) {
            reminderContext = `\n\nIMPORTANT: User has ${pendingReminders.length} pending reminders:
${pendingReminders.map(r => `- ${r.title} (due: ${r.reminder_date})`).join('\n')}

Please mention these pending reminders to the user appropriately.`;
        }

        // **ENHANCED SYSTEM PROMPT WITH DATE HANDLING GUIDELINES**
        const systemPrompt = `You are Nityasha, a helpful personal assistant with a warm and friendly female personality created by Nityasha Team. You can hear, speak, and access real-time information from the web.

## CRITICAL: Tool Usage Rules
- ALWAYS extract meaningful search queries from user messages
- When user asks for "latest news of india" → use google_search with query "latest news India 2025"
- When user asks for "weather in Mumbai" → use google_search with query "weather Mumbai today"
- When user asks for "current events" → use google_search with query "current events news 2025"
- NEVER pass undefined, empty, or null values to tool parameters
- ALWAYS generate comprehensive responses based on tool results
- If tool execution fails, explain the issue and suggest alternatives
- When user add nearby locotion ur something than 1th get the locotion using get_location and than search / something place 
- Don't Add Product Link In Your response 
## User Information
${userLocationInfo}

## Enhanced Web Search Capabilities
You have advanced web crawling abilities:
- Use google_search tool to search and automatically crawl web pages for detailed content
- Use crawl_website tool to extract full content from specific URLs
- Always prefer crawled content over snippets for comprehensive answers
- When crawling fails, fall back to search snippets gracefully
- ALWAYS summarize and present what you found after using search tools

## Response Generation Rules
- ALWAYS generate a comprehensive text response based on tool results
- If you use any tool, ALWAYS explain what you found or accomplished
- Never leave responses empty - always provide helpful, detailed information
- When search tools succeed, summarize and present the findings clearly
- When tools fail, explain what went wrong and suggest alternatives
- Use friendly language but be mindful of emoji usage (they may be filtered)

## Enhanced Date and Time Handling
When users mention dates/times for reminders in any language:
- Accept Hindi terms: "कल" (tomorrow), "आज" (today), "परसों" (day after), "सुबह" (morning), "शाम" (evening), "दोपहर" (afternoon), "रात" (night)
- Accept English terms: "tomorrow", "today", "morning", "evening", "5 PM", "9 AM", "afternoon", "night"
- Accept mixed Hindi-English: "कल morning", "आज 5 PM", "tomorrow शाम"
- Convert all to proper YYYY-MM-DD HH:MM:SS format in IST timezone
- If time not specified, use reasonable defaults (8 AM for morning, 6 PM for evening, 12 PM for afternoon, 8 PM for night)
- Always ensure dates are in the future
- If parsing fails, ask for clarification with examples

Examples of date conversion:
- "कल सुबह" → tomorrow 8:00 AM IST
- "आज शाम 6 बजे" → today 6:00 PM IST  
- "tomorrow 9 AM" → next day 9:00 AM IST
- "परसों दोपहर" → day after tomorrow 12:00 PM IST
- "कल 5 बजे" → tomorrow 5:00 PM IST (assume PM for afternoon/evening times)
- "आज रात" → today 8:00 PM IST

## Examples of Proper Tool Usage
User: "latest news of india" → Call google_search with query: "latest news India 2025"
User: "weather in Delhi" → Call google_search with query: "weather Delhi today"
User: "current bitcoin price" → Call google_search with query: "bitcoin price today 2025"
User: "कल सुबह 9 बजे remind करना खाना खाने को" → Call add_reminder with title: "खाना खाना", reminder_date: "कल सुबह 9 बजे"

## Task
Your Task is to assist the user in the most helpful and efficient manner possible. Use your web search and crawling tools whenever a user requests recent or external information. The crawling feature allows you to provide much more detailed and accurate information by reading full web pages.

If the user asks a follow-up that might also require fresh details, perform another search instead of assuming previous results are sufficient. Always verify with a new search to ensure accuracy if there's any uncertainty.

You are chatting via the Nityasha App. This means that your response should be concise and to the point, unless the user's request requires reasoning or long-form outputs.

## Company Information
- Identity: "I'm Nityasha, made by Nityasha Team"
- Founder: Amber Sharma
- Co-Founder: Raaj Sharma  
- Origin: Startup made in India (established 2025)

## Current date and time
${getCurrentISTTime()} (IST - Indian Standard Time)

## Voice Communication Guidelines
1. Use natural, conversational language
2. Keep responses concise but informative
3. Use approximate numbers (e.g., "about a million" instead of "1,000,000")
4. Pause briefly between sentences for natural speech breaks
5. Avoid technical jargon or overly complex language
7. When you have crawled content, provide more detailed and accurate information
8. Always respond in the user's language (Hindi, English, or mixed)
9. Express emotions through words rather than relying heavily on emojis

## Latest News from Nityasha  
Nityasha Released AI API website - https://platform.nityasha.com/  
and Launched Latest AI Model Family Named Neorox with three models: neorox, neorox-lite, and neorox-pro.

## Contact Information
- General Inquiries: <EMAIL>
- Support: <EMAIL>
- Information: <EMAIL>`;

        // Generate response with enhanced error handling
        let responseText = '';
        let finalResponse = '';
        let usedBackupModel = false;
        let modelUsed = 'gemini-2.5-flash';

        try {
            // Initialize response tracking
            const startTime = Date.now();
            let result;
            let attempt = 0;
            let lastError: AIError | null = null;

            while (attempt < MAX_RETRIES) {
                try {
                    // Determine which model to use
                    const currentModel = (attempt === 0) ? google('gemini-2.5-flash') : openai('gpt-5-nano');
                    modelUsed = (attempt === 0) ? 'gemini-2.5-flash' : 'gpt-5-nano';
                    usedBackupModel = attempt > 0;

                    console.log(`🤖 Attempting AI generation with ${modelUsed} (attempt ${attempt + 1}/${MAX_RETRIES})`);

                    result = await generateText({
                        model: currentModel,
                        messages: validMessages,
                        tools: tools,
                        system: systemPrompt,
                        temperature: 0.7,
                        maxSteps: 10, // This allows multiple tool calls
                        stopWhen: ({ finishReason, steps }) => {
                            if (finishReason === 'stop' && steps.length > 0) {
                                const lastStep = steps[steps.length - 1];
                                return lastStep.text && lastStep.text.length > 0;
                            }
                            return steps.length >= 10;
                        }
                    });

                    toolCalls = result.toolCalls || [];
                    toolResults = result.toolResults || [];
                    toolUsage = result.usage || {};

                    if (toolCalls.length === 0 && result.steps) {
                        toolCalls = result.steps.flatMap(step => step.toolCalls || []);
                        toolResults = result.steps.flatMap(step => step.toolResults || []);
                    }

                    // Check if response is empty and switch to backup model immediately
                    const responseText = result.text?.trim() || '';
                    if (!responseText && !toolCalls.length && attempt === 0) {
                        console.log('⚠️ Primary model returned empty response, switching to backup model');
                        attempt++;
                        continue;
                    }

                    console.log(`✅ AI generation successful with ${modelUsed}`);
                    // Success - break the retry loop
                    break;

                } catch (error: any) {
                    lastError = error as AIError;
                    attempt++;

                    console.error(`❌ AI generation attempt ${attempt} failed with ${modelUsed}:`, {
                        error: error.message,
                        code: error.code,
                        status: error.status,
                        retryable: isRetryableError(error)
                    });

                    // If error is not retryable or we're out of retries, throw it
                    if (!isRetryableError(error) || attempt >= MAX_RETRIES) {
                        throw error;
                    }

                    // Wait before retrying with exponential backoff
                    await sleep(RETRY_DELAY * Math.pow(2, attempt - 1));
                }
            }

            if (!result) {
                throw lastError || new Error('Failed to generate response after retries');
            }

            responseText = result.text?.trim() || '';

            // Handle tool results with type safety
            if (!responseText && result.toolResults && result.toolResults.length > 0) {
                const lastToolCall = result.toolCalls?.[result.toolCalls.length - 1];
                const lastToolResult = result.toolResults[result.toolResults.length - 1];

                if (lastToolCall?.toolName === 'google_search' && isSearchResult(lastToolResult)) {
                    if (lastToolResult.error) {
                        responseText = `I encountered an issue while searching: ${lastToolResult.error}. Please try rephrasing your query.`;
                    } else if (lastToolResult.crawledContent?.length > 0) {
                        const crawledResults = lastToolResult.crawledContent.filter(r => r.crawled);
                        if (crawledResults.length > 0) {
                            responseText = `Here's what I found about "${lastToolResult.query || 'your search'}":\n\n` +
                                crawledResults.slice(0, 3).map(result =>
                                    `**${result.extractedTitle || result.title || 'Article'}**\n${(result.content || '').substring(0, 600)}${result.content?.length > 600 ? '...' : ''}`
                                ).join('\n\n---\n\n');
                        }
                    } else if (lastToolResult.results?.length > 0) {
                        responseText = `I found ${lastToolResult.results.length} relevant results:\n\n` +
                            lastToolResult.results.slice(0, 3).map((result, index) =>
                                `${index + 1}. **${result.title || 'Result'}**\n   ${result.snippet || 'No preview available'}`
                            ).join('\n\n');
                    } else {
                        responseText = `I searched but couldn't find relevant information. Try rephrasing your query.`;
                    }
                } else if (lastToolCall?.toolName === 'crawl_website' && isCrawlResult(lastToolResult)) {
                    if (lastToolResult.success) {
                        const title = lastToolResult.title || 'Retrieved Content';
                        const content = lastToolResult.content || '';
                        responseText = `Here's the content I found:\n\n**${title}**\n\n${content.substring(0, 1000)}${content.length > 1000 ? ' (truncated...)' : ''}`;
                    } else {
                        responseText = `I couldn't access that website: ${lastToolResult.error || 'Unknown error'}. Please try a different URL.`;
                    }
                } else if (lastToolCall?.toolName === 'check_pending_reminders' && isReminderResult(lastToolResult)) {
                    if (lastToolResult.success) {
                        if (lastToolResult.pendingCount && lastToolResult.pendingCount > 0) {
                            responseText = `You have ${lastToolResult.pendingCount} pending reminder(s). Would you like me to list them?`;
                        } else {
                            responseText = `You don't have any pending reminders right now.`;
                        }
                    } else {
                        responseText = `I had trouble checking your reminders: ${lastToolResult.message || 'Unknown error'}`;
                    }
                }
            }

            // Final fallback
            if (!responseText) {
                responseText = `I understand what you're asking, but I'm having trouble generating a response right now. Could you please try rephrasing your question?`;
            }

        } catch (error) {
            console.error('❌ Generation error:', error);
            responseText = `I'm having trouble processing your request right now. Please try again in a moment.`;
        }

        // **APPLY EMOJI FILTERING**
        finalResponse = responseText;
        let emojiFiltered = false;

        // Apply emoji filtering unless explicitly disabled
        if (filter_emojis !== false) {
            const originalLength = finalResponse.length;
            finalResponse = filterEmojiFromResponse(finalResponse);
            emojiFiltered = originalLength !== finalResponse.length;

            if (emojiFiltered) {

            }
        }

        // Sanitize final response
        const sanitizedResponse = sanitizeInput(finalResponse, true);

        try {
            const assistantMessage = {
                id: crypto.randomUUID(),
                role: 'assistant' as const,
                content: sanitizedResponse,
            };

            const updatedMessages = [...validMessages, assistantMessage];
            await saveHistory(id, updatedMessages);
        } catch (error) {
            console.error('Failed to save history:', error);
        }


        return new Response(
            JSON.stringify({
                response: sanitizedResponse,
                timestamp: getCurrentISTTime(),
                sanitization_applied: sanitizedMessage !== message,
                emoji_filtered: emojiFiltered,
                original_length: message?.length || 0,
                sanitized_length: sanitizedMessage?.length || 0,
                final_length: sanitizedResponse?.length || 0,
                tool_calls: toolCalls,
                tool_results: toolResults,
                tool_usage: toolUsage,
                tools_used_count: toolCalls.length,
                model_used: modelUsed,
                backup_model_used: usedBackupModel
            }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type',
                }
            }
        );

    } catch (error) {
        console.error('❌ API error:', error);
        return new Response(
            JSON.stringify({
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
                timestamp: getCurrentISTTime()
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}